GHuOoZUG4MHatjFoQwTF5pAqjUdio1d30V53_6P61SE
# Coze工作流图片处理工具

## 功能说明
这是一个基于Coze平台API的图片处理工具，使用OAuth认证方式调用Coze工作流来处理图片（如生成证件照等）。

## 工作流程
1. 使用OAuth认证获取访问令牌
2. 上传原始图片到Coze平台
3. 调用指定的工作流处理图片
4. 下载处理后的图片并保存到本地

## 认证配置
### OAuth认证信息
- 公钥（kid）：`GHuOoZUG4MHatjFoQwTF5pAqjUdio1d30V53_6P61SE`
- 私钥：需要存储在 `private_key.pem` 文件中

### OAuth JWT鉴权流程图

```mermaid
graph TD
    subgraph 第一步: JWT认证
        A[客户端] -->|1. 读取| B[private_key.pem]
        B -->|2. 生成| C[JWT Token]
        C -->|3. 包含| D[Header + Payload]
        D -->|4. 发送| E[Coze服务器]
        E -->|5. 验证| F{验证JWT}
        F -->|成功| G[返回access_token]
        F -->|失败| H[返回401错误]
    end

    subgraph 第二步: API调用
        I[客户端] -->|1. 带上access_token| J[上传图片API]
        J -->|2. 获得| K[file_id]
        K -->|3. 调用| L[工作流API]
        L -->|4. 返回| M[处理后的图片URL]
        M -->|5. 下载| N[保存到本地]
    end

    style 第一步 fill:#f9f,stroke:#333,stroke-width:2px
    style 第二步 fill:#bbf,stroke:#333,stroke-width:2px
```

### JWT详细结构
```mermaid
graph LR
    subgraph JWT结构
        A[JWT Token] -->|组成| B[Header]
        A -->|组成| C[Payload]
        A -->|组成| D[Signature]
        
        B -->|包含| E["kid: 公钥指纹<br/>alg: RS256<br/>typ: JWT"]
        C -->|包含| F["iss: 应用ID<br/>aud: api.coze.cn<br/>exp: 过期时间<br/>jti: 随机字符串"]
        D -->|使用| G[私钥签名]
    end

    style JWT结构 fill:#f9f,stroke:#333,stroke-width:2px
```

## 工作流参数说明
工作流需要以下参数：

### 必填参数
1. `input_describe`: String类型，描述文本
2. `input_image`: Image类型，输入图片
3. `input_yasuo_befor`: String类型，压缩前的相关参数
4. `input_yasuo_after`: String类型，压缩后的相关参数
5. `befor_daxiao`: String类型，处理前的大小参数
6. `befor_kongjian`: String类型，处理前的空间参数
7. `after_daxiao`: String类型，处理后的大小参数
8. `after_kongjian`: String类型，处理后的空间参数

示例：
```python
parameters = {
    "input_describe": "证件照处理",
    "input_image": {
        "file_id": "your_file_id"
    },
    "input_yasuo_befor": "原图压缩参数",
    "input_yasuo_after": "处理后压缩参数",
    "befor_daxiao": "原图大小",
    "befor_kongjian": "原图空间",
    "after_daxiao": "处理后大小",
    "after_kongjian": "处理后空间"
}
```

## 使用方法

### 1. 准备工作
- 确保有正确的OAuth认证信息（公钥和私钥）
- 将私钥保存为 `private_key.pem` 文件

### 2. 配置参数
在`main`函数中设置以下参数：
```python
client_id = "1158039563886"  # OAuth 应用的 ID
private_key_path = "private_key.pem"  # 私钥文件路径
workflow_id = "7489471548211904547"  # 工作流ID
save_path = "your_save_path"  # 处理后图片的保存路径
```

### 3. 运行脚本
```bash
python testCozeImageGeneration.py
```

## API说明

### OAuth认证
- 端点：`https://api.coze.cn/api/permission/oauth2/token`
- 方法：POST
- 认证：JWT Bearer Token
- 返回：访问令牌

### 文件上传API
- 端点：`https://api.coze.cn/v1/files/upload`
- 方法：POST
- 认证：Bearer Token
- 返回：文件ID

### 工作流执行API
- 端点：`https://api.coze.cn/v1/workflow/run`
- 方法：POST
- 认证：Bearer Token
- 参数：
  - `workflow_id`：工作流ID
  - `parameters`：工作流参数
  - `is_async`：是否异步执行

## 注意事项
1. 图片大小限制：上传的图片不能超过512MB
2. 支持的图片格式：JPG、JPG2、PNG、GIF、WEBP、HEIC、HEIF、BMP、PCD、TIFF
3. 文件有效期：上传到Coze平台的文件有效期为3个月
4. 访问令牌有效期：OAuth访问令牌有效期为24小时

## 错误处理
- JWT生成失败：检查私钥文件和认证信息
- 认证失败：检查公钥和私钥是否匹配
- 文件上传失败：检查文件格式和大小
- 工作流执行失败：确保所有必填参数都已提供

## 开发历程
1. 实现OAuth认证流程，使用JWT获取访问令牌
2. 实现文件上传功能，处理文件ID的获取
3. 实现工作流调用，添加所有必需参数
4. 添加结果处理和图片保存功能
5. 完善错误处理和日志输出
6. 测试和调试，确保功能正常运行 