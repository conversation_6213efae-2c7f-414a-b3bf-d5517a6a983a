#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环境设置脚本
用于创建测试文件夹和模拟图片文件
"""

import os
import shutil
from PIL import Image, ImageDraw, ImageFont
import random

def create_test_image(file_path, text, size=(400, 300), bg_color=(255, 255, 255)):
    """创建一个带文字的测试图片"""
    try:
        # 创建图片
        img = Image.new('RGB', size, bg_color)
        draw = ImageDraw.Draw(img)
        
        # 尝试使用系统字体，如果没有就使用默认字体
        try:
            # Windows系统字体
            font = ImageFont.truetype("arial.ttf", 40)
        except:
            try:
                # 备用字体
                font = ImageFont.truetype("C:/Windows/Fonts/simhei.ttf", 40)
            except:
                # 默认字体
                font = ImageFont.load_default()
        
        # 计算文字位置（居中）
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        x = (size[0] - text_width) // 2
        y = (size[1] - text_height) // 2
        
        # 绘制文字
        draw.text((x, y), text, fill=(0, 0, 0), font=font)
        
        # 保存图片
        img.save(file_path, 'JPEG', quality=95)
        print(f"✅ 创建测试图片: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建图片失败 {file_path}: {str(e)}")
        return False

def setup_test_environment():
    """设置测试环境"""
    print("🚀 开始设置测试环境...")
    
    # 测试文件夹路径
    base_path = os.path.join(os.getcwd(), "test_data")
    folder1_path = os.path.join(base_path, "folder1")
    folder2_path = os.path.join(base_path, "folder2")
    output_path = os.path.join(base_path, "output")
    
    # 创建文件夹
    for folder in [folder1_path, folder2_path, output_path]:
        if os.path.exists(folder):
            shutil.rmtree(folder)
        os.makedirs(folder)
        print(f"📁 创建文件夹: {folder}")
    
    # 在folder1中创建5张测试图片
    print("\n📸 在folder1中创建测试图片...")
    for i in range(1, 6):
        file_path = os.path.join(folder1_path, f"image1_{i}.jpg")
        create_test_image(file_path, f"图片1-{i}", bg_color=(200, 220, 255))
    
    # 在folder2中创建8张测试图片
    print("\n📸 在folder2中创建测试图片...")
    colors = [
        (255, 200, 200),  # 红色系
        (200, 255, 200),  # 绿色系
        (255, 255, 200),  # 黄色系
        (255, 200, 255),  # 紫色系
        (200, 255, 255),  # 青色系
        (255, 220, 180),  # 橙色系
        (220, 180, 255),  # 蓝紫色系
        (180, 255, 220),  # 青绿色系
    ]
    
    for i in range(1, 9):
        file_path = os.path.join(folder2_path, f"image2_{i}.jpg")
        bg_color = colors[i-1] if i <= len(colors) else (200, 200, 200)
        create_test_image(file_path, f"图片2-{i}", bg_color=bg_color)
    
    print(f"\n✅ 测试环境设置完成！")
    print(f"📁 文件夹1路径: {folder1_path}")
    print(f"📁 文件夹2路径: {folder2_path}")
    print(f"📁 输出路径: {output_path}")
    
    return folder1_path, folder2_path, output_path

def list_test_files():
    """列出测试文件"""
    base_path = os.path.join(os.getcwd(), "test_data")
    
    print("\n📋 测试文件列表:")
    for folder_name in ["folder1", "folder2"]:
        folder_path = os.path.join(base_path, folder_name)
        if os.path.exists(folder_path):
            files = [f for f in os.listdir(folder_path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            print(f"\n{folder_name} ({len(files)}张图片):")
            for file in sorted(files):
                print(f"  - {file}")

if __name__ == "__main__":
    try:
        # 检查PIL库
        print("🔍 检查依赖库...")
        
        folder1, folder2, output = setup_test_environment()
        list_test_files()
        
        print(f"\n🎯 测试参数设置:")
        print(f'glv["folder1_path"] = r"{folder1}"')
        print(f'glv["folder2_path"] = r"{folder2}"')
        print(f'glv["save_path"] = r"{output}"')
        
    except ImportError as e:
        print(f"❌ 缺少依赖库: {e}")
        print("请安装PIL库: pip install Pillow")
    except Exception as e:
        print(f"❌ 设置失败: {str(e)}")
