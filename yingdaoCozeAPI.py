# 使用提醒:
# 1. xbot包提供软件自动化、数据表格、Excel、日志、AI等功能
# 2. package包提供访问当前应用数据的功能，如获取元素、访问全局变量、获取资源文件等功能
# 3. 当此模块作为流程独立运行时执行main函数
# 4. 可视化流程中可以通过"调用模块"的指令使用此模块

import xbot
from xbot import print, sleep
from xbot import robot
from .import package
from .package import variables as glv
import os
import json
from datetime import datetime
import requests
import jwt
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend
import uuid
import time
import random

# 全局常量
WORKFLOW_API_URL = "https://api.coze.cn/v1/workflow/run"
UPLOAD_API_URL = "https://api.coze.cn/v1/files/upload"
WORKFLOW_ID = "7523631914998480906"
TOKEN_API_URL = "https://api.coze.cn/api/permission/oauth2/token"
CLIENT_ID = "1158039563886"  # OAuth应用的ID
PRIVATE_KEY_PATH = r"D:\Cursor\07_yingdao\01_Cozediaoyong\private_key.pem"  # 私钥路径
KID = "GHuOoZUG4MHatjFoQwTF5pAqjUdio1d30V53_6P61SE"  # 公钥指纹

def generate_jwt():
    """生成JWT token"""
    try:
        # 读取私钥
        with open(PRIVATE_KEY_PATH, 'rb') as f:
            private_key = serialization.load_pem_private_key(
                f.read(),
                password=None,
                backend=default_backend()
            )
        
        # 设置JWT的payload
        now = int(time.time())
        payload = {
            'iss': CLIENT_ID,
            'aud': 'api.coze.cn',
            'iat': now,
            'exp': now + 3600,
            'jti': str(uuid.uuid4())
        }
        
        # 生成JWT
        token = jwt.encode(
            payload,
            private_key,
            algorithm='RS256',
            headers={
                'alg': 'RS256',
                'typ': 'JWT',
                'kid': KID
            }
        )
        
        print(f"生成的JWT token: {token}")
        return token
        
    except Exception as e:
        print(f"生成JWT token失败: {str(e)}")
        return None

def get_access_token():
    """获取OAuth Access Token"""
    try:
        # 生成JWT
        jwt_token = generate_jwt()
        if not jwt_token:
            return None

        # 准备请求
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {jwt_token}'
        }
        
        data = {
            'grant_type': 'urn:ietf:params:oauth:grant-type:jwt-bearer',
            'duration_seconds': 86399  # 24小时
        }
        
        # 发送请求获取access token
        response = requests.post(
            TOKEN_API_URL,
            headers=headers,
            json=data
        )
        
        if response.status_code != 200:
            print(f"获取access token失败，状态码：{response.status_code}")
            print(f"错误信息：{response.text}")
            return None
            
        result = response.json()
        access_token = result.get('access_token')
        
        # 保存access_token到全局变量
        glv["access_token"] = access_token
        print(f"成功获取access token")
        return access_token
        
    except Exception as e:
        print(f"获取access token失败: {str(e)}")
        return None

def upload_single_image(image_path, access_token):
    """上传单张图片文件到Coze平台"""
    try:
        print(f"开始上传图片：{image_path}")
        headers = {
            "Authorization": f"Bearer {access_token}"
        }

        with open(image_path, 'rb') as f:
            files = {
                'file': (os.path.basename(image_path), f, 'image/jpeg')
            }
            response = requests.post(
                url=UPLOAD_API_URL,
                headers=headers,
                files=files
            )

        if response.status_code != 200:
            print(f"文件上传失败，状态码：{response.status_code}")
            return None

        result = response.json()
        if result.get("code") != 0:
            print(f"文件上传失败：{result.get('msg')}")
            return None

        file_id = result.get("data", {}).get("id")
        if not file_id:
            print("未能获取文件ID")
            return None

        print(f"文件上传成功，文件ID: {file_id}")
        return file_id

    except Exception as e:
        print(f"文件上传过程中发生错误: {str(e)}")
        return None

def get_images_from_folders():
    """从文件夹中随机获取4张图片路径"""
    try:
        folder1_path = glv.get("folder1_path")
        folder2_path = glv.get("folder2_path")

        if not folder1_path or not folder2_path:
            print("错误：folder1_path和folder2_path不能为空")
            return None

        if not os.path.exists(folder1_path) or not os.path.exists(folder2_path):
            print("错误：指定的文件夹路径不存在")
            return None

        # 支持的图片格式
        image_extensions = ('.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff')

        # 从文件夹1随机获取第1张图片
        folder1_files = [f for f in os.listdir(folder1_path)
                        if f.lower().endswith(image_extensions)]
        if not folder1_files:
            print(f"错误：文件夹1 ({folder1_path}) 中没有找到图片文件")
            return None

        # 随机选择1张图片
        selected_image1 = random.choice(folder1_files)
        image1_path = os.path.join(folder1_path, selected_image1)
        print(f"从文件夹1随机选择图片：{selected_image1}")

        # 从文件夹2随机获取第2、3、4张图片
        folder2_files = [f for f in os.listdir(folder2_path)
                        if f.lower().endswith(image_extensions)]
        if len(folder2_files) < 3:
            print(f"错误：文件夹2 ({folder2_path}) 中至少需要3张图片，当前只有{len(folder2_files)}张")
            return None

        # 随机选择3张不重复的图片
        selected_images = random.sample(folder2_files, 3)
        image2_path = os.path.join(folder2_path, selected_images[0])
        image3_path = os.path.join(folder2_path, selected_images[1])
        image4_path = os.path.join(folder2_path, selected_images[2])

        print(f"从文件夹2随机选择图片：")
        for i, img_name in enumerate(selected_images, 2):
            print(f"  图片{i}: {img_name}")

        image_paths = [image1_path, image2_path, image3_path, image4_path]
        print(f"\n最终选择的图片路径：")
        for i, path in enumerate(image_paths, 1):
            print(f"  jianli{i}: {os.path.basename(path)} -> {path}")

        return image_paths

    except Exception as e:
        print(f"获取图片路径时发生错误: {str(e)}")
        return None

def generate_and_save_image():
    """处理图片生成"""
    try:
        # 从全局变量获取参数
        access_token = glv.get("access_token")
        save_path = glv.get("save_path")

        # 参数验证
        if not save_path:
            print("错误：save_path是必填参数")
            return False

        # 获取4张图片路径
        image_paths = get_images_from_folders()
        if not image_paths:
            print("获取图片路径失败，无法继续处理")
            return False

        # 上传4张图片获取file_id
        file_ids = []
        for i, image_path in enumerate(image_paths, 1):
            file_id = upload_single_image(image_path, access_token)
            if not file_id:
                print(f"第{i}张图片上传失败，无法继续处理")
                return False
            file_ids.append(file_id)

        print(f"所有图片上传成功，获得file_ids: {file_ids}")
        
        print(f"开始处理图片...")

        # 准备请求头
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

        # 准备请求体 - 新工作流需要4张图片参数
        # 尝试标准的Image对象格式
        data = {
            "workflow_id": WORKFLOW_ID,
            "parameters": {
                "jianli1": {
                    "type": "image",
                    "file_id": file_ids[0]
                },
                "jianli2": {
                    "type": "image",
                    "file_id": file_ids[1]
                },
                "jianli3": {
                    "type": "image",
                    "file_id": file_ids[2]
                },
                "jianli4": {
                    "type": "image",
                    "file_id": file_ids[3]
                }
            },
            "is_async": False
        }
        
        print(f"发送的数据：{json.dumps(data, ensure_ascii=False)}")
        
        # 调用工作流
        response = requests.post(
            url=WORKFLOW_API_URL,
            headers=headers,
            json=data
        )
        
        if response.status_code != 200:
            print(f"API调用失败，状态码：{response.status_code}")
            return False
            
        result = response.json()
        if result.get("code") != 0:
            print(f"工作流执行失败：{result.get('msg')}")
            return False
        
        output_data = result.get("data")
        output_json = json.loads(output_data)

        # 获取8张输出图片的URL
        image_urls = {
            "image1": output_json.get("image1"),
            "image2": output_json.get("image2"),
            "image3": output_json.get("image3"),
            "image4": output_json.get("image4"),
            "image5": output_json.get("image5"),
            "image6": output_json.get("image6"),
            "image7": output_json.get("image7"),
            "image8": output_json.get("image8")
        }
        
        # 添加详细的日志输出
        print(f"API返回的完整数据: {json.dumps(output_json, ensure_ascii=False, indent=2)}")
        
        # 检查每个URL是否存在
        missing_urls = []
        for image_type, url in image_urls.items():
            if not url:
                missing_urls.append(image_type)
                print(f"警告：未获取到{image_type}的图片URL")
        
        if missing_urls:
            print(f"警告：以下图片URL缺失: {', '.join(missing_urls)}")
            # 不再因为缺失URL而返回False，继续处理可用的图片
        
        print(f"获取到图片URLs: {json.dumps(image_urls, ensure_ascii=False)}")
        
        # 确保save_path是绝对路径
        save_path = os.path.abspath(save_path)
        
        # 如果save_path不存在，创建它
        if not os.path.exists(save_path):
            print(f"创建保存目录：{save_path}")
            os.makedirs(save_path)
        
        # 下载并保存8张图片
        saved_files = {}
        for image_type, url in image_urls.items():
            if not url:  # 跳过空URL
                print(f"警告：{image_type}的URL为空，跳过下载")
                continue

            try:
                # 下载图片
                response = requests.get(url)
                if response.status_code != 200:
                    print(f"下载{image_type}图片失败，状态码: {response.status_code}")
                    continue

                # 直接使用键名作为文件名
                filename = f"{image_type}.png"

                # 构建完整的文件路径
                file_path = os.path.join(save_path, filename)
                print(f"准备保存{image_type}图片到：{file_path}")

                # 保存图片
                with open(file_path, 'wb') as f:
                    f.write(response.content)

                print(f"{image_type}图片已成功保存到: {file_path}")
                saved_files[image_type] = file_path

            except Exception as e:
                print(f"保存{image_type}图片时发生错误: {str(e)}")
                continue
        
        # 保存结果路径到全局变量
        glv["result_paths"] = saved_files
        return True if len(saved_files) > 0 else False  # 只要保存了至少一张图片就返回True
        
    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        return False

def main(_):  # 添加一个占位参数
    """主入口函数
    Args:
        _: 影刀RPA传入的参数，本模块不使用
    """
    try:
        # 首先获取access_token
        if not get_access_token():
            print("获取access token失败，无法继续执行")
            return False
            
        # 继续执行原有的图片处理流程
        return generate_and_save_image()
    except Exception as e:
        print(f"执行过程中发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 测试用例
    glv["folder1_path"] = "D:/test/folder1"  # 第1张图片的文件夹
    glv["folder2_path"] = "D:/test/folder2"  # 第2、3、4张图片的文件夹
    glv["save_path"] = "D:/output"  # 输出文件夹

    result = main(None)
    print(f"测试结果：{result}")
