import os
import requests
from datetime import datetime
import json
import jwt
import time
import uuid
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend

class ImageGenerationWorkflow:
    def __init__(self, client_id, private_key_path, workflow_id, save_path, bot_id=None):
        self.client_id = client_id
        self.private_key_path = private_key_path
        self.workflow_id = workflow_id
        self.save_path = save_path
        self.bot_id = bot_id
        self.workflow_api_url = "https://api.coze.cn/v1/workflow/run"
        self.upload_api_url = "https://api.coze.cn/v1/files/upload"
        self.token_api_url = "https://api.coze.cn/api/permission/oauth2/token"
        
        # 确保保存目录存在
        if not os.path.exists(save_path):
            os.makedirs(save_path)
            
        # 获取access token
        self.access_token = self._get_access_token()

    def _generate_jwt(self):
        """生成JWT"""
        try:
            # 读取私钥
            with open(self.private_key_path, 'rb') as f:
                private_key = serialization.load_pem_private_key(
                    f.read(),
                    password=None,
                    backend=default_backend()
                )
            
            # 设置JWT的payload
            now = int(time.time())
            payload = {
                'iss': self.client_id,  # OAuth 应用的 ID
                'aud': 'api.coze.cn',   # 扣子 API 的Endpoint
                'iat': now,             # JWT开始生效的时间
                'exp': now + 3600,      # JWT过期时间（1小时）
                'jti': str(uuid.uuid4()) # 随机字符串，防止重放攻击
            }
            
            # 生成JWT
            token = jwt.encode(
                payload,
                private_key,
                algorithm='RS256',
                headers={
                    'alg': 'RS256',
                    'typ': 'JWT',
                    'kid': "GHuOoZUG4MHatjFoQwTF5pAqjUdio1d30V53_6P61SE"  # 公钥指纹
                }
            )
            
            print(f"生成的JWT token: {token}")
            return token
            
        except Exception as e:
            print(f"生成JWT token失败: {str(e)}")
            return None

    def _get_access_token(self):
        """获取OAuth Access Token"""
        try:
            # 生成JWT
            jwt_token = self._generate_jwt()
            if not jwt_token:
                return None

            # 准备请求
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {jwt_token}'
            }
            
            data = {
                'grant_type': 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'duration_seconds': 86399  # 24小时
            }
            
            # 发送请求获取access token
            response = requests.post(
                self.token_api_url,
                headers=headers,
                json=data
            )
            
            print(f"获取access token响应: {response.text}")
            
            if response.status_code != 200:
                print(f"获取access token失败，状态码：{response.status_code}")
                print(f"错误信息：{response.text}")
                return None
                
            result = response.json()
            return result.get('access_token')
            
        except Exception as e:
            print(f"获取access token失败: {str(e)}")
            return None

    def upload_image(self, image_path):
        """上传图片文件到Coze平台"""
        try:
            print(f"开始上传图片：{image_path}")
            
            # 准备请求头
            headers = {
                "Authorization": f"Bearer {self.access_token}"
            }
            
            print("发送上传请求...")
            print(f"请求头: {headers}")
            
            # 准备文件
            with open(image_path, 'rb') as f:
                files = {
                    'file': (os.path.basename(image_path), f, 'image/jpeg')  # 根据实际文件类型调整
                }
                
                # 发送上传请求
                response = requests.post(
                    self.upload_api_url,
                    headers=headers,
                    files=files
                )
                
                print(f"响应状态码：{response.status_code}")
                print(f"响应内容：{response.text}")
                
                if response.status_code != 200:
                    print(f"文件上传失败，状态码：{response.status_code}")
                    print(f"错误信息：{response.text}")
                    return None
                    
                result = response.json()
                if result.get("code") != 0:
                    print(f"文件上传失败：{result.get('msg')}")
                    return None
                
                file_id = result.get("data", {}).get("id")
                if not file_id:
                    print("未能获取文件ID")
                    return None
                    
                print(f"文件上传成功，文件ID: {file_id}")
                return file_id
                
        except Exception as e:
            print(f"文件上传过程中发生错误: {str(e)}")
            return None

    def generate_and_save_image(self, input_describe, input_image_path):
        """处理图片生成
        
        Args:
            input_describe: str, 必填，描述文本
            input_image_path: str, 必填，输入图片的本地路径
        """
        try:
            if not input_describe:
                print("错误：input_describe是必填参数")
                return None
                
            if not input_image_path or not os.path.exists(input_image_path):
                print("错误：input_image_path不存在")
                return None
            
            # 先上传图片获取file_id
            file_id = self.upload_image(input_image_path)
            if not file_id:
                print("图片上传失败，无法继续处理")
                return None
            
            print(f"开始处理图片...")
            print(f"描述文本：{input_describe}")
            
            # 准备请求头
            headers = {
                "Authorization": f"Bearer {self.access_token}",
                "Content-Type": "application/json"
            }
            
            # 准备请求体
            data = {
                "workflow_id": self.workflow_id,
                "parameters": {
                    "input_describe": input_describe,
                    "input_image": {
                        "file_id": file_id
                    },
                    "input_yasuo_befor": "原图压缩参数",
                    "input_yasuo_after": "处理后压缩参数",
                    "befor_daxiao": "原图大小",
                    "befor_kongjian": "原图空间",
                    "after_daxiao": "处理后大小",
                    "after_kongjian": "处理后空间"
                },
                "is_async": False
            }
            
            # 如果有bot_id，添加到请求中
            if self.bot_id:
                data["bot_id"] = self.bot_id
            
            print("发送工作流请求...")
            print(f"请求头: {headers}")
            print(f"请求体: {json.dumps(data, ensure_ascii=False)}")
            
            # 调用工作流
            response = requests.post(self.workflow_api_url, headers=headers, json=data)
            
            if response.status_code != 200:
                print(f"API调用失败，状态码：{response.status_code}")
                print(f"错误信息：{response.text}")
                return None
                
            result = response.json()
            
            # 检查返回结果
            if result.get("code") != 0:
                print(f"工作流执行失败：{result.get('msg')}")
                return None
            
            # 打印调试URL
            print(f"调试URL：{result.get('debug_url')}")
            
            # 解析返回的数据
            output_data = result.get("data")
            print(f"工作流返回结果：{output_data}")
            
            # 从返回数据中提取output字段的URL
            try:
                output_json = json.loads(output_data)
                image_url = output_json.get("output")
                
                if not image_url:
                    print("未在返回数据中找到output URL")
                    return None
                
                print(f"获取到图片URL: {image_url}")
                
                # 下载图片
                img_response = requests.get(image_url)
                if img_response.status_code == 200:
                    # 生成文件名
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"generated_image_{timestamp}.png"
                    file_path = os.path.join(self.save_path, filename)
                    
                    # 保存图片
                    with open(file_path, 'wb') as f:
                        f.write(img_response.content)
                    
                    print(f"图片已成功保存到: {file_path}")
                    return file_path
                else:
                    print(f"下载图片失败，状态码: {img_response.status_code}")
                    return None
                
            except json.JSONDecodeError:
                print("解析返回数据失败")
                return None
            except Exception as e:
                print(f"处理返回数据时发生错误: {str(e)}")
                return None
                
        except Exception as e:
            print(f"发生错误: {str(e)}")
            return None

def main():
    # 配置参数
    client_id = "1158039563886"  # OAuth 应用的 ID
    private_key_path = "private_key.pem"  # 私钥文件路径
    workflow_id = "7489471548211904547"  # 工作流ID
    save_path = r"D:\这有1000万\01_CozeTuPian"   # 图片保存路径
    
    # 创建工作流实例
    workflow = ImageGenerationWorkflow(client_id, private_key_path, workflow_id, save_path)
    
    # 测试工作流
    input_describe = "你好好证件照测才哈哈哈"  # 必填：描述文本
    input_image_path = r"D:\我好厉害\touxiang.jpeg"  # 必填：输入图片路径
    
    print("\n" + "="*50)
    result_path = workflow.generate_and_save_image(
        input_describe=input_describe,
        input_image_path=input_image_path
    )
    if result_path:
        print(f"成功处理图片：{result_path}")
    else:
        print("图片处理失败")
    print("="*50 + "\n")

if __name__ == "__main__":
    main() 