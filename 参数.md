# Coze API 参数说明文档

## 必填参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| access_token | String | Coze平台的访问令牌 | "pat_xxxxxx" |
| input_describe | String | 图片处理的描述文本 | "证件照处理" |
| input_image_path | String | 输入图片的本地路径 | "D:/images/photo.jpg" |
| save_path | String | 处理后图片的保存路径 | "D:/output" |

## 可选参数

| 参数名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| input_yasuo_befor | String | 压缩前的相关参数 | "" |
| input_yasuo_after | String | 压缩后的相关参数 | "" |
| befor_daxiao | String | 处理前的大小参数 | "" |
| befor_kongjian | String | 处理前的空间参数 | "" |
| after_daxiao | String | 处理后的大小参数 | "" |
| after_kongjian | String | 处理后的空间参数 | "" |

## 系统常量

| 常量名 | 值 | 说明 |
|--------|-----|------|
| WORKFLOW_API_URL | "https://api.coze.cn/v1/workflow/run" | Coze工作流API地址 |
| UPLOAD_API_URL | "https://api.coze.cn/v1/files/upload" | Coze文件上传API地址 |
| WORKFLOW_ID | "7489471548211904547" | 工作流ID |

## 返回参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| file_id | String | 上传文件后返回的ID | "file_xxxxx" |
| result_path | String | 处理后图片的完整保存路径 | "D:/output/generated_image_20240406_123456.png" |

## 使用示例

```python
# 设置必填参数
glv["access_token"] = "your_access_token"
glv["input_describe"] = "证件照处理"
glv["input_image_path"] = "test.jpg"
glv["save_path"] = "output"

# 设置可选参数（如需要）
glv["input_yasuo_befor"] = "压缩前参数"
glv["input_yasuo_after"] = "压缩后参数"
glv["befor_daxiao"] = "处理前大小"
glv["befor_kongjian"] = "处理前空间"
glv["after_daxiao"] = "处理后大小"
glv["after_kongjian"] = "处理后空间"
```

## 注意事项

1. 必填参数缺失会导致程序执行失败
2. 可选参数如不需要可以不设置，默认为空字符串
3. 文件路径建议使用绝对路径，避免路径解析错误
4. 所有参数都通过全局变量（glv）进行传递 