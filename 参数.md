# Coze API 参数说明文档 (新工作流版本)

## 必填参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| folder1_path | String | 第1张图片所在的文件夹路径 | "D:/images/folder1" |
| folder2_path | String | 第2、3、4张图片所在的文件夹路径 | "D:/images/folder2" |
| save_path | String | 处理后图片的保存路径 | "D:/output" |

## 系统常量

| 常量名 | 值 | 说明 |
|--------|-----|------|
| WORKFLOW_API_URL | "https://api.coze.cn/v1/workflow/run" | Coze工作流API地址 |
| UPLOAD_API_URL | "https://api.coze.cn/v1/files/upload" | Coze文件上传API地址 |
| WORKFLOW_ID | "7523631914998480906" | 新工作流ID |

## 输入图片要求

| 图片位置 | 参数名 | 说明 |
|----------|--------|------|
| folder1_path中的第1张图片 | jianli1 | 工作流的第1个图片参数 |
| folder2_path中的第1张图片 | jianli2 | 工作流的第2个图片参数 |
| folder2_path中的第2张图片 | jianli3 | 工作流的第3个图片参数 |
| folder2_path中的第3张图片 | jianli4 | 工作流的第4个图片参数 |

## 输出图片

| 输出字段 | 保存文件名 | 说明 |
|----------|------------|------|
| image1 | image1.png | 第1张输出图片 |
| image2 | image2.png | 第2张输出图片 |
| image3 | image3.png | 第3张输出图片 |
| image4 | image4.png | 第4张输出图片 |
| image5 | image5.png | 第5张输出图片 |
| image6 | image6.png | 第6张输出图片 |
| image7 | image7.png | 第7张输出图片 |
| image8 | image8.png | 第8张输出图片 |

## 返回参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| result_paths | Dict | 所有保存的图片路径字典 | {"image1": "D:/output/image1.png", ...} |

## 使用示例

```python
# 设置必填参数
glv["folder1_path"] = "D:/images/folder1"  # 包含第1张图片的文件夹
glv["folder2_path"] = "D:/images/folder2"  # 包含第2、3、4张图片的文件夹
glv["save_path"] = "D:/output"  # 输出文件夹

# 调用主函数
result = main(None)
```

## 注意事项

1. folder1_path文件夹中至少需要1张图片
2. folder2_path文件夹中至少需要3张图片
3. 支持的图片格式：jpg, jpeg, png, gif, webp, bmp, tiff
4. 程序会自动选择文件夹中的前几张图片
5. 文件路径建议使用绝对路径，避免路径解析错误
6. 所有参数都通过全局变量（glv）进行传递
7. 输出8张图片，全部保存为PNG格式