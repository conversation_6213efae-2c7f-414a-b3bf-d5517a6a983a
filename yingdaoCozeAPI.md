# 影刀RPA Python模块开发经验总结

## 1. 模块结构规范

### 1.1 main函数定义
```python
def main(_):  # 必须接收一个参数
    """主入口函数
    Args:
        _: 影刀RPA传入的参数，可以不使用
    """
    try:
        return your_function()
    except Exception as e:
        print(f"执行过程中发生错误: {str(e)}")
        return False
```

### 1.2 必要的导入
```python
import xbot
from xbot import print, sleep
from .import package
from .package import variables as glv
```

## 2. 全局变量使用

### 2.1 设置全局变量
```python
# 正确方式
glv["variable_name"] = value

# 错误方式（不支持）
glv.set("variable_name", value)
```

### 2.2 获取全局变量
```python
value = glv.get("variable_name")
```

## 3. HTTP请求处理

### 3.1 使用requests库
- 影刀RPA环境中可以直接使用`requests`库
- 需要在模块开头导入：`import requests`

### 3.2 文件上传示例
```python
headers = {
    "Authorization": f"Bearer {access_token}"
}
with open(input_image_path, 'rb') as f:
    files = {
        'file': (os.path.basename(input_image_path), f, 'image/jpeg')
    }
    response = requests.post(
        url=upload_url,
        headers=headers,
        files=files
    )
```

### 3.3 JSON请求示例
```python
headers = {
    "Authorization": f"Bearer {access_token}",
    "Content-Type": "application/json"
}
data = {
    "key": "value"
}
response = requests.post(
    url=api_url,
    headers=headers,
    json=data  # 使用json参数，会自动处理序列化
)
```

## 4. 文件路径处理

### 4.1 路径验证和创建
```python
# 确保save_path是绝对路径
save_path = os.path.abspath(save_path)

# 创建目录（如果不存在）
if not os.path.exists(save_path):
    os.makedirs(save_path)

# 构建文件路径
file_path = os.path.join(save_path, filename)
```

### 4.2 文件保存最佳实践
```python
try:
    with open(file_path, 'wb') as f:
        f.write(response.content)
except Exception as e:
    print(f"保存文件时发生错误: {str(e)}")
    return False
```

## 5. 错误处理

### 5.1 异常捕获
- 每个主要函数都应该有try-except块
- 打印详细的错误信息
- 返回False表示失败，True表示成功

### 5.2 参数验证
```python
if not input_path or not os.path.exists(input_path):
    print("错误：input_path不存在")
    return False
```

## 6. 调试技巧

### 6.1 日志输出
```python
print(f"开始处理：{input_path}")
print(f"参数信息：{json.dumps(data, ensure_ascii=False)}")
print(f"响应状态码：{response.status_code}")
```

### 6.2 响应检查
```python
if response.status_code != 200:
    print(f"请求失败，状态码：{response.status_code}")
    print(f"错误信息：{response.text}")
    return False
```

## 7. 注意事项

1. 模块中的main函数必须接收一个参数，即使不使用
2. 使用全局变量时使用字典方式赋值
3. 文件路径处理要考虑绝对路径和目录创建
4. HTTP请求推荐使用requests库
5. 所有的关键操作都要有错误处理和日志输出
6. 返回值统一使用True/False表示成功/失败 